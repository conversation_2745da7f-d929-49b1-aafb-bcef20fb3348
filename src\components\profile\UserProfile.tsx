import { useState, useRef, useEffect } from "react";
import { User } from "@supabase/supabase-js";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { useProfile } from "@/hooks/useProfile";
import { Camera, User as UserIcon, Mail, Save, Loader2 } from "lucide-react";

interface UserProfileProps {
  user: User;
}

const UserProfile = ({ user }: UserProfileProps) => {
  const { profile, loading, uploading, updateProfile, uploadAvatar } = useProfile(user);
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
  });
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize form data when profile loads
  useEffect(() => {
    if (profile) {
      setFormData({
        firstName: profile.firstName,
        lastName: profile.lastName,
      });
    }
  }, [profile]);

  const handleSave = async () => {
    const result = await updateProfile(formData);
    if (result.success) {
      setIsEditing(false);
    }
  };

  const handleCancel = () => {
    if (profile) {
      setFormData({
        firstName: profile.firstName,
        lastName: profile.lastName,
      });
    }
    setIsEditing(false);
  };

  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        return;
      }
      
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        return;
      }

      await uploadAvatar(file);
    }
  };

  const getInitials = () => {
    const firstName = profile?.firstName || '';
    const lastName = profile?.lastName || '';
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  if (loading) {
    return (
      <Card className="bg-slate-800/80 backdrop-blur-xl border-green-700/40" style={{ backgroundColor: 'rgba(30, 41, 59, 0.9)' }}>
        <CardContent className="flex items-center justify-center p-8">
          <Loader2 className="h-8 w-8 animate-spin text-green-400" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      className="backdrop-blur-xl"
      style={{
        backgroundColor: 'rgba(30, 41, 59, 0.95)',
        border: '1px solid rgba(34, 197, 94, 0.4)',
        color: 'white'
      }}
    >
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <UserIcon className="h-5 w-5 text-green-400" />
          Información Personal
        </CardTitle>
        <CardDescription className="text-green-200/80">
          Gestiona tu información de perfil y foto
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Avatar Section */}
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Avatar className="h-20 w-20 border-2 border-green-400/30">
              <AvatarImage src={profile?.avatarUrl} alt="Avatar" />
              <AvatarFallback className="bg-slate-700 text-white text-lg font-semibold">
                {getInitials() || 'U'}
              </AvatarFallback>
            </Avatar>
            <Button
              size="sm"
              variant="outline"
              className="absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0 bg-green-600 hover:bg-green-700 border-green-500"
              onClick={handleAvatarClick}
              disabled={uploading}
            >
              {uploading ? (
                <Loader2 className="h-4 w-4 animate-spin text-white" />
              ) : (
                <Camera className="h-4 w-4 text-white" />
              )}
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              className="hidden"
            />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-white">
              {profile?.firstName} {profile?.lastName}
            </h3>
            <p className="text-green-200/80 flex items-center gap-1">
              <Mail className="h-4 w-4" />
              {profile?.email}
            </p>
            <p className="text-sm text-slate-400 mt-1">
              Haz clic en la cámara para cambiar tu foto de perfil
            </p>
          </div>
        </div>

        <Separator className="bg-green-700/20" />

        {/* Personal Information Form */}
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName" className="text-green-100 font-medium">
                Nombre
              </Label>
              <Input
                id="firstName"
                value={formData.firstName}
                onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                disabled={!isEditing}
                className="bg-slate-700/30 border-green-600/30 text-white placeholder:text-slate-400 focus:border-green-400 focus:ring-green-400/20 disabled:opacity-60"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="lastName" className="text-green-100 font-medium">
                Apellido
              </Label>
              <Input
                id="lastName"
                value={formData.lastName}
                onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                disabled={!isEditing}
                className="bg-slate-700/30 border-green-600/30 text-white placeholder:text-slate-400 focus:border-green-400 focus:ring-green-400/20 disabled:opacity-60"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="email" className="text-green-100 font-medium">
              Correo electrónico
            </Label>
            <Input
              id="email"
              value={profile?.email || ''}
              disabled
              className="bg-slate-700/30 border-green-600/30 text-white placeholder:text-slate-400 opacity-60"
            />
            <p className="text-xs text-slate-400">
              El correo electrónico no se puede cambiar desde aquí
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3">
          {isEditing ? (
            <>
              <Button
                variant="outline"
                onClick={handleCancel}
                className="border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                Cancelar
              </Button>
              <Button
                onClick={handleSave}
                className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
              >
                <Save className="h-4 w-4 mr-2" />
                Guardar cambios
              </Button>
            </>
          ) : (
            <Button
              onClick={() => setIsEditing(true)}
              className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
            >
              Editar información
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default UserProfile;
