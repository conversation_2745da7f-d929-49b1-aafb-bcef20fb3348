import React, { createContext, useContext, useState, useEffect } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/sonner';

interface AuthState {
  user: User | null;
  session: Session | null;
  loading: boolean;
}

interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

interface LoginData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

interface AuthContextType extends AuthState {
  register: (data: RegisterData) => Promise<{ success: boolean; needsConfirmation?: boolean; error?: string }>;
  login: (data: LoginData) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<{ success: boolean; error?: string }>;
  resetPassword: (email: string) => Promise<{ success: boolean; error?: string }>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    loading: true,
  });
  
  // Track if this is a new registration to show appropriate message
  const [isNewRegistration, setIsNewRegistration] = useState(false);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) {
        console.error('Error getting session:', error);
      }
      setAuthState({
        user: session?.user ?? null,
        session,
        loading: false,
      });
    };

    getInitialSession();

    // Listen for auth changes - ONLY ONE LISTENER FOR THE ENTIRE APP
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setAuthState({
          user: session?.user ?? null,
          session,
          loading: false,
        });

        if (event === 'SIGNED_IN') {
          if (isNewRegistration) {
            toast.success("🧪 ¡Cuenta creada exitosamente!", {
              description: "Bienvenido al laboratorio de Pasteur. ¡Ya puedes explorar!",
              duration: 7000,
            });
            setIsNewRegistration(false);
          } else {
            toast.success("🎉 ¡Bienvenido de vuelta!", {
              description: "Has iniciado sesión exitosamente en Pasteur",
              duration: 6000,
            });
          }
        } else if (event === 'SIGNED_OUT') {
          toast.success("👋 Sesión cerrada", {
            description: "Has cerrado sesión correctamente",
            duration: 6000,
          });
        }
      }
    );

    return () => subscription.unsubscribe();
  }, [isNewRegistration]);

  const register = async (data: RegisterData) => {
    try {
      const { data: authData, error } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          data: {
            first_name: data.firstName,
            last_name: data.lastName,
          },
        },
      });

      if (error) {
        throw error;
      }

      // Since email confirmation is disabled, user should be automatically signed in
      if (authData.user) {
        setIsNewRegistration(true);
        // Notification will be shown by onAuthStateChange event
        return { success: true, needsConfirmation: false };
      }

      // This shouldn't happen with email confirmation disabled, but just in case
      setIsNewRegistration(true);
      return { success: true, needsConfirmation: false };
    } catch (error: any) {
      let errorMessage = "Error al crear la cuenta";
      
      if (error.message.includes('already registered')) {
        errorMessage = "Este email ya está registrado";
      } else if (error.message.includes('Password should be')) {
        errorMessage = "La contraseña debe tener al menos 6 caracteres";
      } else if (error.message.includes('Invalid email')) {
        errorMessage = "Email inválido";
      }

      toast.error("❌ Error en el registro", {
        description: errorMessage,
        duration: 6000,
      });
      return { success: false, error: errorMessage };
    }
  };

  const login = async (data: LoginData) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password,
      });

      if (error) {
        throw error;
      }

      // Notification will be shown by onAuthStateChange event
      return { success: true };
    } catch (error: any) {
      let errorMessage = "Error al iniciar sesión";
      
      if (error.message.includes('Invalid login credentials')) {
        errorMessage = "Email o contraseña incorrectos";
      } else if (error.message.includes('Email not confirmed')) {
        errorMessage = "Por favor, confirma tu email antes de iniciar sesión";
      }

      toast.error("🔒 Error de acceso", {
        description: errorMessage,
        duration: 6000,
      });
      return { success: false, error: errorMessage };
    }
  };

  const logout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        throw error;
      }
      return { success: true };
    } catch (error: any) {
      toast.error("⚠️ Error al cerrar sesión", {
        description: error.message,
        duration: 6000,
      });
      return { success: false, error: error.message };
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) {
        throw error;
      }

      toast.success("📧 Email enviado", {
        description: "Revisa tu bandeja de entrada para restablecer tu contraseña",
        duration: 8000,
      });
      return { success: true };
    } catch (error: any) {
      toast.error("❌ Error de recuperación", {
        description: error.message,
        duration: 6000,
      });
      return { success: false, error: error.message };
    }
  };

  const value: AuthContextType = {
    user: authState.user,
    session: authState.session,
    loading: authState.loading,
    register,
    login,
    logout,
    resetPassword,
    isAuthenticated: !!authState.user,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
