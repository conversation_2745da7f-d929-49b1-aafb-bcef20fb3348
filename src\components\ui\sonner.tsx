import { useTheme } from "next-themes"
import { Toaster as Sonner, toast } from "sonner"

type ToasterProps = React.ComponentProps<typeof Sonner>

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme="dark"
      className="toaster group"
      position="top-right"
      closeButton={true}
      richColors={true}
      expand={true}
      visibleToasts={6}
      gap={8}
      offset={16}
      duration={5000}
      toastOptions={{
        dismissible: true,
        closeButton: true,
        classNames: {
          toast:
            "group toast group-[.toaster]:bg-slate-900/95 group-[.toaster]:backdrop-blur-xl group-[.toaster]:text-white group-[.toaster]:border group-[.toaster]:shadow-2xl group-[.toaster]:rounded-lg group-[.toaster]:min-h-[60px]",
          title: "group-[.toast]:text-white group-[.toast]:font-semibold group-[.toast]:text-sm group-[.toast]:leading-tight",
          description: "group-[.toast]:text-slate-300 group-[.toast]:font-normal group-[.toast]:text-xs group-[.toast]:leading-relaxed group-[.toast]:mt-1",
          actionButton:
            "group-[.toast]:bg-green-600 group-[.toast]:text-white group-[.toast]:hover:bg-green-500 group-[.toast]:rounded-md group-[.toast]:transition-all group-[.toast]:font-medium group-[.toast]:text-xs group-[.toast]:px-3 group-[.toast]:py-1.5",
          cancelButton:
            "group-[.toast]:bg-slate-600 group-[.toast]:text-slate-200 group-[.toast]:hover:bg-slate-500 group-[.toast]:rounded-md group-[.toast]:transition-all group-[.toast]:font-medium group-[.toast]:text-xs group-[.toast]:px-3 group-[.toast]:py-1.5",
          success: "group-[.toaster]:border-green-500/40 group-[.toaster]:bg-slate-900/95",
          error: "group-[.toaster]:border-red-500/40 group-[.toaster]:bg-slate-900/95",
          warning: "group-[.toaster]:border-yellow-500/40 group-[.toaster]:bg-slate-900/95",
          info: "group-[.toaster]:border-blue-500/40 group-[.toaster]:bg-slate-900/95",
          loading: "group-[.toaster]:border-slate-500/40 group-[.toaster]:bg-slate-900/95",
        },
        style: {
          background: 'rgba(15, 23, 42, 0.95)',
          backdropFilter: 'blur(16px)',
          border: '1px solid rgba(71, 85, 105, 0.3)',
          color: 'white',
          borderRadius: '8px',
          padding: '12px 16px',
          minHeight: '60px',
          boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1)',
        }
      }}
      {...props}
    />
  )
}

export { Toaster, toast }
