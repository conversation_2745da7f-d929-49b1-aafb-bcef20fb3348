// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://fasyuyditfskkoefkvku.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZhc3l1eWRpdGZza2tvZWZrdmt1Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwNTY1OTAsImV4cCI6MjA2NDYzMjU5MH0.0Ciy-QDfv9I0Q1dglHfwXP0U1wXkIPziBLq8Vm9DHaQ";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);