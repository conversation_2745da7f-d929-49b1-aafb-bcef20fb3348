import { useState } from "react";
import { User } from "@supabase/supabase-js";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useProfile } from "@/hooks/useProfile";
import { 
  Bell, 
  Shield, 
  Key, 
  Trash2, 
  Settings, 
  Mail, 
  Smartphone, 
  Eye, 
  EyeOff,
  AlertTriangle,
  Save
} from "lucide-react";

interface ProfileSettingsProps {
  user: User;
}

const ProfileSettings = ({ user }: ProfileSettingsProps) => {
  const { settings, updateSettings, changePassword } = useProfile(user);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [showPasswords, setShowPasswords] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);

  const handleNotificationChange = (key: keyof typeof settings.notifications, value: boolean) => {
    updateSettings({
      notifications: {
        ...settings.notifications,
        [key]: value,
      },
    });
  };

  const handlePrivacyChange = (key: keyof typeof settings.privacy, value: any) => {
    updateSettings({
      privacy: {
        ...settings.privacy,
        [key]: value,
      },
    });
  };

  const handlePasswordChange = async () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      return;
    }

    if (passwordForm.newPassword.length < 6) {
      return;
    }

    setIsChangingPassword(true);
    const result = await changePassword(passwordForm.currentPassword, passwordForm.newPassword);
    
    if (result.success) {
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      });
    }
    setIsChangingPassword(false);
  };

  return (
    <div className="space-y-6">
      {/* Notifications Settings */}
      <Card className="bg-slate-800/80 backdrop-blur-xl border-green-700/40" style={{ backgroundColor: 'rgba(30, 41, 59, 0.9)' }}>
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Bell className="h-5 w-5 text-green-400" />
            Notificaciones
          </CardTitle>
          <CardDescription className="text-green-200/80">
            Configura cómo y cuándo recibir notificaciones
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-green-100 font-medium flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Notificaciones por email
              </Label>
              <p className="text-sm text-slate-400">
                Recibe actualizaciones importantes por correo electrónico
              </p>
            </div>
            <Switch
              checked={settings.notifications.email}
              onCheckedChange={(value) => handleNotificationChange('email', value)}
              className="data-[state=checked]:bg-green-500"
            />
          </div>

          <Separator className="bg-green-700/20" />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-green-100 font-medium flex items-center gap-2">
                <Smartphone className="h-4 w-4" />
                Notificaciones push
              </Label>
              <p className="text-sm text-slate-400">
                Recibe notificaciones en tiempo real en tu navegador
              </p>
            </div>
            <Switch
              checked={settings.notifications.push}
              onCheckedChange={(value) => handleNotificationChange('push', value)}
              className="data-[state=checked]:bg-green-500"
            />
          </div>

          <Separator className="bg-green-700/20" />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-green-100 font-medium flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Notificaciones generales
              </Label>
              <p className="text-sm text-slate-400">
                Recibe notificaciones sobre actualizaciones del sistema
              </p>
            </div>
            <Switch
              checked={settings.notifications.general}
              onCheckedChange={(value) => handleNotificationChange('general', value)}
              className="data-[state=checked]:bg-green-500"
            />
          </div>
        </CardContent>
      </Card>

      {/* Privacy Settings */}
      <Card className="bg-slate-800/80 backdrop-blur-xl border-green-700/40" style={{ backgroundColor: 'rgba(30, 41, 59, 0.9)' }}>
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Shield className="h-5 w-5 text-green-400" />
            Privacidad
          </CardTitle>
          <CardDescription className="text-green-200/80">
            Controla la visibilidad de tu perfil y datos
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label className="text-green-100 font-medium">
              Visibilidad del perfil
            </Label>
            <Select
              value={settings.privacy.profileVisibility}
              onValueChange={(value: 'public' | 'private') => 
                handlePrivacyChange('profileVisibility', value)
              }
            >
              <SelectTrigger className="bg-slate-700/30 border-green-600/30 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-slate-800 border-green-700/20">
                <SelectItem value="public" className="text-white hover:bg-slate-700">
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    Público - Visible para todos
                  </div>
                </SelectItem>
                <SelectItem value="private" className="text-white hover:bg-slate-700">
                  <div className="flex items-center gap-2">
                    <EyeOff className="h-4 w-4" />
                    Privado - Solo para ti
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator className="bg-green-700/20" />

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-green-100 font-medium">
                Mostrar email en perfil
              </Label>
              <p className="text-sm text-slate-400">
                Permite que otros usuarios vean tu dirección de correo
              </p>
            </div>
            <Switch
              checked={settings.privacy.showEmail}
              onCheckedChange={(value) => handlePrivacyChange('showEmail', value)}
              className="data-[state=checked]:bg-green-500"
            />
          </div>
        </CardContent>
      </Card>

      {/* Password Change */}
      <Card className="bg-slate-800/80 backdrop-blur-xl border-green-700/40" style={{ backgroundColor: 'rgba(30, 41, 59, 0.9)' }}>
        <CardHeader>
          <CardTitle className="text-white flex items-center gap-2">
            <Key className="h-5 w-5 text-green-400" />
            Cambiar contraseña
          </CardTitle>
          <CardDescription className="text-green-200/80">
            Actualiza tu contraseña para mantener tu cuenta segura
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="currentPassword" className="text-green-100 font-medium">
              Contraseña actual
            </Label>
            <div className="relative">
              <Input
                id="currentPassword"
                type={showCurrentPassword ? "text" : "password"}
                value={passwordForm.currentPassword}
                onChange={(e) => setPasswordForm(prev => ({ ...prev, currentPassword: e.target.value }))}
                className="pr-10 bg-slate-700/30 border-green-600/30 text-white placeholder:text-slate-400 focus:border-green-400 focus:ring-green-400/20"
                placeholder="Ingresa tu contraseña actual"
              />
              <button
                type="button"
                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-400 hover:text-green-300 transition-colors duration-200"
              >
                {showCurrentPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="newPassword" className="text-green-100 font-medium">
              Nueva contraseña
            </Label>
            <div className="relative">
              <Input
                id="newPassword"
                type={showNewPassword ? "text" : "password"}
                value={passwordForm.newPassword}
                onChange={(e) => setPasswordForm(prev => ({ ...prev, newPassword: e.target.value }))}
                className="pr-10 bg-slate-700/30 border-green-600/30 text-white placeholder:text-slate-400 focus:border-green-400 focus:ring-green-400/20"
                placeholder="Ingresa tu nueva contraseña"
              />
              <button
                type="button"
                onClick={() => setShowNewPassword(!showNewPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-400 hover:text-green-300 transition-colors duration-200"
              >
                {showNewPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword" className="text-green-100 font-medium">
              Confirmar nueva contraseña
            </Label>
            <div className="relative">
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                value={passwordForm.confirmPassword}
                onChange={(e) => setPasswordForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                className="pr-10 bg-slate-700/30 border-green-600/30 text-white placeholder:text-slate-400 focus:border-green-400 focus:ring-green-400/20"
                placeholder="Confirma tu nueva contraseña"
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-green-400 hover:text-green-300 transition-colors duration-200"
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4" />
                ) : (
                  <Eye className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="showPasswords"
              checked={showPasswords}
              onCheckedChange={(checked) => {
                setShowPasswords(checked);
                setShowCurrentPassword(checked);
                setShowNewPassword(checked);
                setShowConfirmPassword(checked);
              }}
              className="data-[state=checked]:bg-green-500"
            />
            <Label htmlFor="showPasswords" className="text-green-100 text-sm">
              Mostrar todas las contraseñas
            </Label>
          </div>

          {passwordForm.newPassword && passwordForm.confirmPassword && 
           passwordForm.newPassword !== passwordForm.confirmPassword && (
            <Alert className="border-red-500/50 bg-red-500/10">
              <AlertTriangle className="h-4 w-4 text-red-400" />
              <AlertDescription className="text-red-300">
                Las contraseñas no coinciden
              </AlertDescription>
            </Alert>
          )}

          <Button
            onClick={handlePasswordChange}
            disabled={
              !passwordForm.currentPassword || 
              !passwordForm.newPassword || 
              !passwordForm.confirmPassword ||
              passwordForm.newPassword !== passwordForm.confirmPassword ||
              passwordForm.newPassword.length < 6 ||
              isChangingPassword
            }
            className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
          >
            {isChangingPassword ? (
              <>
                <Settings className="h-4 w-4 mr-2 animate-spin" />
                Cambiando contraseña...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Cambiar contraseña
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Danger Zone */}
      <Card className="bg-slate-800/80 backdrop-blur-xl border-red-700/40" style={{ backgroundColor: 'rgba(30, 41, 59, 0.9)' }}>
        <CardHeader>
          <CardTitle className="text-red-400 flex items-center gap-2">
            <Trash2 className="h-5 w-5" />
            Zona de peligro
          </CardTitle>
          <CardDescription className="text-red-200/80">
            Acciones irreversibles para tu cuenta
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="border-red-500/50 bg-red-500/10 mb-4">
            <AlertTriangle className="h-4 w-4 text-red-400" />
            <AlertDescription className="text-red-300">
              Estas acciones no se pueden deshacer. Procede con precaución.
            </AlertDescription>
          </Alert>
          
          <Button
            variant="destructive"
            className="w-full bg-red-600 hover:bg-red-700 text-white"
            onClick={() => {
              // TODO: Implement account deactivation
              console.log('Deactivate account');
            }}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Desactivar cuenta
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileSettings;
