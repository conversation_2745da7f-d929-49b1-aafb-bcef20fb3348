
import { useState } from "react";
import Login from "@/components/auth/Login";
import Register from "@/components/auth/Register";
import CountryQuiz from "@/components/auth/CountryQuiz";

const Index = () => {
  const [isLogin, setIsLogin] = useState(true);
  const [quizCompleted, setQuizCompleted] = useState(false);

  // No auto-dismiss to allow auth notifications to be visible

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-blue-800 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 rounded-full bg-blue-500/10 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 rounded-full bg-blue-600/10 blur-3xl"></div>
      </div>

      <div className={`w-full relative z-10 ${isLogin ? 'max-w-md' : 'max-w-7xl'}`}>
        <div className="bg-slate-800/40 backdrop-blur-xl rounded-3xl shadow-2xl border border-blue-700/20 p-8 animate-scale-in">
          {isLogin ? (
            <>
              {/* Character Image for Login */}
              <div className="flex justify-center mb-8 animate-fade-in">
                <div className="relative">
                  <img
                    src="/lovable-uploads/louis-pasteur-greeting.png"
                    alt="Character illustration"
                    className="w-52 h-52 object-contain animate-float drop-shadow-2xl"
                  />
                  <div className="absolute inset-0 bg-blue-400/20 rounded-full blur-xl scale-75"></div>
                </div>
              </div>

              {/* Form Container for Login */}
              <div className="animate-slide-in">
                <Login onToggleMode={() => setIsLogin(false)} />
              </div>
            </>
          ) : (
            /* Three Column Layout for Register */
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center min-h-[700px]">
              {/* Character Image for Register */}
              <div className="flex flex-col items-center justify-center animate-fade-in space-y-4">
                <div className="relative">
                  <img
                    src="/lovable-uploads/louis-registro.png"
                    alt="Character illustration"
                    className="w-56 h-56 lg:w-64 lg:h-64 object-contain animate-float drop-shadow-2xl"
                  />
                  <div className="absolute inset-0 bg-blue-400/20 rounded-full blur-xl scale-75"></div>
                </div>
                <div className="text-center space-y-2">
                  <h1 className="text-3xl font-bold text-white tracking-tight">
                    Crea una cuenta!
                  </h1>
                  <p className="text-blue-200/80 text-sm">
                    La suerte favorece a la mente preparada
                  </p>
                </div>
              </div>

              {/* Form Container for Register */}
              <div className="animate-slide-in flex items-center justify-center">
                <div className="w-full">
                  <Register onToggleMode={() => setIsLogin(true)} quizCompleted={quizCompleted} />
                </div>
              </div>

              {/* Quiz Container */}
              <div className="animate-slide-in flex items-center justify-center">
                <div className="w-full">
                  <CountryQuiz onQuizComplete={setQuizCompleted} />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Index;
