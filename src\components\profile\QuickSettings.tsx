import { User } from "@supabase/supabase-js";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { useProfile } from "@/hooks/useProfile";
import { toast } from "@/components/ui/sonner";
import {
  Bell,
  Shield,
  Zap,
  Moon,
  Sun,
  Volume2,
  VolumeX,
  TestTube
} from "lucide-react";
import { useState } from "react";

interface QuickSettingsProps {
  user: User;
}

const QuickSettings = ({ user }: QuickSettingsProps) => {
  const { settings, updateSettings } = useProfile(user);
  const [darkMode, setDarkMode] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);

  const testProfileNotifications = () => {
    // Crear notificaciones apilables del perfil con timing
    toast.success("👤 Perfil Actualizado", {
      description: "Tu información personal se ha guardado correctamente",
      duration: 5000,
    });

    setTimeout(() => {
      toast("🔔 Configuración Aplicada", {
        description: "Las preferencias de notificaciones han sido actualizadas",
        duration: 5000,
      });
    }, 300);

    setTimeout(() => {
      toast.warning("🔒 Verificación de Seguridad", {
        description: "Se recomienda cambiar tu contraseña cada 90 días",
        duration: 6000,
      });
    }, 600);

    setTimeout(() => {
      toast.success("📸 Avatar Sincronizado", {
        description: "Tu foto de perfil se ha actualizado en todos los dispositivos",
        duration: 5000,
      });
    }, 900);
  };

  const quickActions = [
    {
      id: 'notifications',
      title: 'Notificaciones',
      description: 'Activar/desactivar todas las notificaciones',
      icon: settings.notifications.general ? Bell : VolumeX,
      checked: settings.notifications.general,
      action: () => updateSettings({
        notifications: {
          ...settings.notifications,
          general: !settings.notifications.general,
          email: !settings.notifications.general,
          push: !settings.notifications.general,
        }
      })
    },
    {
      id: 'privacy',
      title: 'Perfil Privado',
      description: 'Hacer tu perfil visible solo para ti',
      icon: Shield,
      checked: settings.privacy.profileVisibility === 'private',
      action: () => updateSettings({
        privacy: {
          ...settings.privacy,
          profileVisibility: settings.privacy.profileVisibility === 'private' ? 'public' : 'private'
        }
      })
    },
    {
      id: 'theme',
      title: 'Modo Oscuro',
      description: 'Cambiar entre tema claro y oscuro',
      icon: darkMode ? Moon : Sun,
      checked: darkMode,
      action: () => {
        setDarkMode(!darkMode);
        // En una implementación real, aquí cambiarías el tema
      }
    },
    {
      id: 'sound',
      title: 'Sonidos',
      description: 'Activar/desactivar efectos de sonido',
      icon: soundEnabled ? Volume2 : VolumeX,
      checked: soundEnabled,
      action: () => setSoundEnabled(!soundEnabled)
    }
  ];

  return (
    <Card className="bg-slate-800/80 backdrop-blur-xl border-green-700/40" style={{ backgroundColor: 'rgba(30, 41, 59, 0.9)' }}>
      <CardHeader>
        <CardTitle className="text-white flex items-center gap-2">
          <Zap className="h-5 w-5 text-green-400" />
          Configuración Rápida
        </CardTitle>
        <CardDescription className="text-green-200/80">
          Accesos directos a las configuraciones más utilizadas
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {quickActions.map((action) => {
            const IconComponent = action.icon;
            return (
              <div
                key={action.id}
                className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg border border-green-600/20 hover:border-green-500/40 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${action.checked ? 'bg-green-600/20' : 'bg-slate-600/20'}`}>
                    <IconComponent className={`h-4 w-4 ${action.checked ? 'text-green-400' : 'text-slate-400'}`} />
                  </div>
                  <div>
                    <p className="text-white font-medium text-sm">{action.title}</p>
                    <p className="text-slate-400 text-xs">{action.description}</p>
                  </div>
                </div>
                <Switch
                  checked={action.checked}
                  onCheckedChange={action.action}
                  className="data-[state=checked]:bg-green-500"
                />
              </div>
            );
          })}
        </div>

        <div className="mt-6 pt-4 border-t border-green-700/20">
          <div className="flex flex-wrap gap-2">
            <Button
              size="sm"
              variant="outline"
              className="border-green-600/30 text-green-100 hover:bg-green-600/20 hover:border-green-500"
              onClick={() => {
                // Activar todas las notificaciones
                updateSettings({
                  notifications: {
                    email: true,
                    push: true,
                    general: true,
                  }
                });
              }}
            >
              🔔 Activar Todo
            </Button>
            <Button
              size="sm"
              variant="outline"
              className="border-slate-600/30 text-slate-300 hover:bg-slate-600/20 hover:border-slate-500"
              onClick={() => {
                // Desactivar todas las notificaciones
                updateSettings({
                  notifications: {
                    email: false,
                    push: false,
                    general: false,
                  }
                });
              }}
            >
              🔕 Silenciar Todo
            </Button>
            <Button
              size="sm"
              variant="outline"
              className="border-blue-600/30 text-blue-100 hover:bg-blue-600/20 hover:border-blue-500"
              onClick={() => {
                // Configuración recomendada
                updateSettings({
                  notifications: {
                    email: true,
                    push: true,
                    general: true,
                  },
                  privacy: {
                    profileVisibility: 'public',
                    showEmail: false,
                  }
                });
              }}
            >
              ⭐ Configuración Recomendada
            </Button>
            <Button
              size="sm"
              variant="outline"
              className="border-purple-600/30 text-purple-100 hover:bg-purple-600/20 hover:border-purple-500"
              onClick={testProfileNotifications}
            >
              <TestTube className="h-4 w-4 mr-1" />
              Probar Notificaciones
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickSettings;
