import { useState, useEffect } from 'react';
import { User } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/components/ui/sonner';

interface ProfileData {
  firstName: string;
  lastName: string;
  email: string;
  avatarUrl?: string;
  notificationsEnabled: boolean;
  emailNotifications: boolean;
  pushNotifications: boolean;
  profileVisibility: 'public' | 'private';
}

interface ProfileSettings {
  notifications: {
    email: boolean;
    push: boolean;
    general: boolean;
  };
  privacy: {
    profileVisibility: 'public' | 'private';
    showEmail: boolean;
  };
}

export const useProfile = (user: User | null) => {
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [settings, setSettings] = useState<ProfileSettings>({
    notifications: {
      email: true,
      push: true,
      general: true,
    },
    privacy: {
      profileVisibility: 'public',
      showEmail: false,
    },
  });
  const [loading, setLoading] = useState(true);
  const [uploading, setUploading] = useState(false);

  useEffect(() => {
    if (user) {
      loadProfile();
    } else {
      setLoading(false);
    }
  }, [user]);

  const loadProfile = async () => {
    try {
      setLoading(true);
      
      // Get user metadata
      const userData = user?.user_metadata || {};
      
      setProfile({
        firstName: userData.first_name || '',
        lastName: userData.last_name || '',
        email: user?.email || '',
        avatarUrl: userData.avatar_url || '',
        notificationsEnabled: true,
        emailNotifications: true,
        pushNotifications: true,
        profileVisibility: 'public',
      });
    } catch (error) {
      console.error('Error loading profile:', error);
      toast.error("⚗️ Error al cargar perfil", {
        description: "No se pudo cargar la información del perfil",
        duration: 4000,
      });
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (data: Partial<ProfileData>) => {
    try {
      if (!user) throw new Error('No user found');

      const { error } = await supabase.auth.updateUser({
        data: {
          first_name: data.firstName,
          last_name: data.lastName,
          avatar_url: data.avatarUrl,
        },
      });

      if (error) throw error;

      setProfile(prev => prev ? { ...prev, ...data } : null);
      
      toast.success("🧪 Perfil actualizado", {
        description: "Los cambios se han guardado exitosamente",
        duration: 4000,
      });

      return { success: true };
    } catch (error: any) {
      console.error('Error updating profile:', error);
      toast.error("⚗️ Error al actualizar", {
        description: error.message || "No se pudo actualizar el perfil",
        duration: 4000,
      });
      return { success: false, error: error.message };
    }
  };

  const uploadAvatar = async (file: File) => {
    try {
      if (!user) throw new Error('No user found');

      setUploading(true);

      // For now, we'll use a placeholder approach since storage bucket needs to be set up
      // In a real implementation, you would:
      // 1. Create the 'avatars' bucket in Supabase Storage
      // 2. Set up proper RLS policies
      // 3. Upload the file and get the public URL

      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // For demo purposes, we'll use a placeholder URL
      const placeholderUrl = `https://api.dicebear.com/7.x/initials/svg?seed=${user.email}&backgroundColor=22c55e`;

      await updateProfile({ avatarUrl: placeholderUrl });

      toast.success("🧪 Avatar actualizado", {
        description: "Se ha configurado un avatar temporal. Configura Supabase Storage para subir imágenes reales.",
        duration: 5000,
        dismissible: true,
        closeButton: true,
      });

      return { success: true, url: placeholderUrl };
    } catch (error: any) {
      console.error('Error uploading avatar:', error);
      toast.error("⚗️ Error al subir imagen", {
        description: error.message || "No se pudo subir la imagen",
        duration: 4000,
        dismissible: true,
        closeButton: true,
      });
      return { success: false, error: error.message };
    } finally {
      setUploading(false);
    }
  };

  const updateSettings = async (newSettings: Partial<ProfileSettings>) => {
    try {
      setSettings(prev => ({
        ...prev,
        ...newSettings,
        notifications: { ...prev.notifications, ...newSettings.notifications },
        privacy: { ...prev.privacy, ...newSettings.privacy },
      }));

      toast.success("🔬 Configuración guardada", {
        description: "Las preferencias se han actualizado",
        duration: 3000,
      });

      return { success: true };
    } catch (error: any) {
      console.error('Error updating settings:', error);
      toast.error("⚗️ Error en configuración", {
        description: "No se pudieron guardar las preferencias",
        duration: 4000,
      });
      return { success: false, error: error.message };
    }
  };

  const changePassword = async (currentPassword: string, newPassword: string) => {
    try {
      // First verify current password by attempting to sign in
      const { error: verifyError } = await supabase.auth.signInWithPassword({
        email: user?.email || '',
        password: currentPassword,
      });

      if (verifyError) {
        throw new Error('Contraseña actual incorrecta');
      }

      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) throw error;

      toast.success("🔐 Contraseña actualizada", {
        description: "Tu contraseña se ha cambiado exitosamente",
        duration: 4000,
      });

      return { success: true };
    } catch (error: any) {
      console.error('Error changing password:', error);
      toast.error("⚗️ Error al cambiar contraseña", {
        description: error.message || "No se pudo cambiar la contraseña",
        duration: 4000,
      });
      return { success: false, error: error.message };
    }
  };

  return {
    profile,
    settings,
    loading,
    uploading,
    updateProfile,
    uploadAvatar,
    updateSettings,
    changePassword,
    refreshProfile: loadProfile,
  };
};
