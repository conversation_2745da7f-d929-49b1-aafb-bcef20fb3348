import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/sonner";
import { ArrowLeft, User, Settings, LogOut } from "lucide-react";
import { useNavigate } from "react-router-dom";
import UserProfile from "@/components/profile/UserProfile";
import ProfileSettings from "@/components/profile/ProfileSettings";
import QuickSettings from "@/components/profile/QuickSettings";
import DarkThemeWrapper from "@/components/profile/DarkThemeWrapper";

const Profile = () => {
  const { user, logout, loading } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("profile");

  // Setup dark mode for profile page (no auto-dismiss to allow auth notifications)
  useEffect(() => {
    // Force dark mode for this page
    document.documentElement.classList.add('dark');
    document.body.style.backgroundColor = '#0f172a';

    return () => {
      // Clean up when leaving the page
      document.body.style.backgroundColor = '';
    };
  }, []);

  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-green-900/20 to-slate-900 flex items-center justify-center">
        <div className="w-8 h-8 border-2 border-green-400/30 border-t-green-400 rounded-full animate-spin"></div>
      </div>
    );
  }

  if (!user) {
    navigate('/');
    return null;
  }

  return (
    <DarkThemeWrapper>
      <div
        className="min-h-screen profile-page"
        style={{
          backgroundColor: '#0f172a',
          backgroundImage: 'radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(16, 185, 129, 0.1) 0%, transparent 50%)',
          color: 'white'
        }}
      >
      {/* Background Effects */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div
          className="absolute top-1/4 left-1/4 w-96 h-96 rounded-full blur-3xl animate-pulse"
          style={{ backgroundColor: 'rgba(34, 197, 94, 0.15)' }}
        ></div>
        <div
          className="absolute bottom-1/4 right-1/4 w-96 h-96 rounded-full blur-3xl animate-pulse"
          style={{ backgroundColor: 'rgba(16, 185, 129, 0.15)', animationDelay: '1s' }}
        ></div>
        <div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 rounded-full blur-3xl animate-pulse"
          style={{ backgroundColor: 'rgba(20, 184, 166, 0.1)', animationDelay: '0.5s' }}
        ></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handleBackToDashboard}
              className="border-green-600/30 text-green-100 hover:bg-green-600/20 hover:border-green-500"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Volver al Dashboard
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-white tracking-tight">
                Mi Perfil
              </h1>
              <p className="text-green-200/80 text-sm">
                Gestiona tu información personal y configuraciones
              </p>
            </div>
          </div>
          
          <Button
            variant="outline"
            onClick={handleLogout}
            className="border-red-600/30 text-red-100 hover:bg-red-600/20 hover:border-red-500"
          >
            <LogOut className="h-4 w-4 mr-2" />
            Cerrar sesión
          </Button>
        </div>

        {/* Main Content */}
        <div
          className="backdrop-blur-xl rounded-3xl shadow-2xl p-6 profile-card"
          style={{
            backgroundColor: 'rgba(30, 41, 59, 0.85)',
            border: '1px solid rgba(34, 197, 94, 0.3)',
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.5)'
          }}
        >
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList
              className="grid w-full grid-cols-3"
              style={{
                backgroundColor: 'rgba(51, 65, 85, 0.9)',
                border: '1px solid rgba(34, 197, 94, 0.3)'
              }}
            >
              <TabsTrigger
                value="profile"
                className="data-[state=active]:bg-green-600/20 data-[state=active]:text-green-100 text-slate-300 hover:text-white transition-colors"
              >
                <User className="h-4 w-4 mr-2" />
                Perfil
              </TabsTrigger>
              <TabsTrigger
                value="settings"
                className="data-[state=active]:bg-green-600/20 data-[state=active]:text-green-100 text-slate-300 hover:text-white transition-colors"
              >
                <Settings className="h-4 w-4 mr-2" />
                Configuración
              </TabsTrigger>
              <TabsTrigger
                value="quick"
                className="data-[state=active]:bg-green-600/20 data-[state=active]:text-green-100 text-slate-300 hover:text-white transition-colors"
              >
                ⚡ Rápido
              </TabsTrigger>
            </TabsList>

            <div className="mt-6">
              <TabsContent value="profile" className="space-y-6">
                <UserProfile user={user} />
              </TabsContent>

              <TabsContent value="settings" className="space-y-6">
                <ProfileSettings user={user} />
              </TabsContent>

              <TabsContent value="quick" className="space-y-6">
                <QuickSettings user={user} />
              </TabsContent>
            </div>
          </Tabs>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center">
          <p className="text-slate-400 text-sm">
            🧪 Laboratorio de Pasteur - Sistema de gestión de perfiles
          </p>
        </div>
      </div>
      </div>
    </DarkThemeWrapper>
  );
};

export default Profile;
