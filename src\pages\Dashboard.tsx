import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { toast } from "@/components/ui/sonner";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { LogOut, User, Mail, Calendar, CheckCircle, Settings } from "lucide-react";

const Dashboard = () => {
  const { user, logout, loading } = useAuth();
  const navigate = useNavigate();

  // No auto-dismiss to allow auth notifications to be visible

  const handleLogout = async () => {
    await logout();
  };

  const goToProfile = () => {
    navigate('/profile');
  };

  const testSonner = () => {
    // Crear notificaciones que se apilarán como en la imagen
    toast.success("✅ Sistema Activado", {
      description: "Esterilización UV activada correctamente",
      duration: 6000,
    });

    setTimeout(() => {
      toast.warning("⚠️ Protocolo de Seguridad", {
        description: "Recuerda usar equipo de protección",
        duration: 6000,
      });
    }, 200);

    setTimeout(() => {
      toast.error("🚨 Alerta de Contaminación", {
        description: "Detectada posible contaminación en sector B-12",
        duration: 7000,
      });
    }, 400);

    setTimeout(() => {
      toast("🔬 Procesando Muestras", {
        description: "Análisis de microorganismos en progreso",
        duration: 6000,
      });
    }, 600);

    setTimeout(() => {
      toast.success("🧪 Experimento Completado", {
        description: "Análisis de muestra #001 finalizado exitosamente",
        duration: 6000,
      });
    }, 800);
  };

  const clearAllToasts = () => {
    toast.dismiss();
    setTimeout(() => {
      toast.success("🧹 Notificaciones limpiadas", {
        description: "Todas las notificaciones han sido cerradas",
        duration: 2000,
      });
    }, 100);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-green-900 to-emerald-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-400"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-green-900 to-emerald-900 p-4">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 rounded-full bg-green-500/10 blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 rounded-full bg-emerald-500/10 blur-3xl"></div>
      </div>

      <div className="relative z-10 max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white">
              ¡Bienvenido al Dashboard!
            </h1>
            <p className="text-green-200/80 mt-2">
              Has completado exitosamente el Pasteur CAPTCHA
            </p>
          </div>
          <div className="flex flex-wrap gap-3">
            <Button
              onClick={goToProfile}
              variant="outline"
              className="bg-green-600/50 border-green-500 text-white hover:bg-green-500/50"
            >
              <Settings className="w-4 h-4 mr-2" />
              Mi Perfil
            </Button>
            <Button
              onClick={testSonner}
              variant="outline"
              className="bg-emerald-600/50 border-emerald-500 text-white hover:bg-emerald-500/50"
            >
              🧪 Probar Notificaciones
            </Button>
            <Button
              onClick={clearAllToasts}
              variant="outline"
              className="bg-orange-600/50 border-orange-500 text-white hover:bg-orange-500/50"
            >
              🧹 Limpiar Todo
            </Button>
            <Button
              onClick={handleLogout}
              variant="outline"
              className="bg-slate-800/50 border-slate-600 text-white hover:bg-slate-700/50"
            >
              <LogOut className="w-4 h-4 mr-2" />
              Cerrar Sesión
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* User Profile Card */}
          <div className="bg-slate-800/40 backdrop-blur-xl rounded-3xl shadow-2xl border border-green-700/20 p-8">
            <div className="flex items-center space-x-4 mb-6">
              <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center">
                <User className="w-8 h-8 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">
                  Perfil de Usuario
                </h2>
                <p className="text-green-200/80 text-sm">
                  Información de tu cuenta
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <Mail className="w-5 h-5 text-green-400" />
                <div>
                  <p className="text-sm text-slate-400">Email</p>
                  <p className="text-white font-medium">{user?.email}</p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <User className="w-5 h-5 text-green-400" />
                <div>
                  <p className="text-sm text-slate-400">Nombre</p>
                  <p className="text-white font-medium">
                    {user?.user_metadata?.first_name} {user?.user_metadata?.last_name}
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <Calendar className="w-5 h-5 text-green-400" />
                <div>
                  <p className="text-sm text-slate-400">Miembro desde</p>
                  <p className="text-white font-medium">
                    {user?.created_at ? new Date(user.created_at).toLocaleDateString('es-ES') : 'N/A'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Achievement Card */}
          <div className="bg-slate-800/40 backdrop-blur-xl rounded-3xl shadow-2xl border border-green-700/20 p-8">
            <div className="flex items-center space-x-4 mb-6">
              <div className="w-16 h-16 bg-emerald-600 rounded-full flex items-center justify-center">
                <CheckCircle className="w-8 h-8 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-white">
                  Logros Desbloqueados
                </h2>
                <p className="text-green-200/80 text-sm">
                  Tus certificaciones completadas
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-green-500/10 rounded-lg border border-green-500/20">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-6 h-6 text-green-500" />
                  <div>
                    <p className="text-white font-medium">Pasteur CAPTCHA</p>
                    <p className="text-green-200/80 text-sm">Verificación de conocimiento completada</p>
                  </div>
                </div>
                <div className="text-green-400 text-sm font-medium">
                  ✓ Completado
                </div>
              </div>

              <div className="flex items-center justify-between p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-6 h-6 text-blue-500" />
                  <div>
                    <p className="text-white font-medium">Experto en Microbiología</p>
                    <p className="text-blue-200/80 text-sm">Conocimiento sobre Louis Pasteur verificado</p>
                  </div>
                </div>
                <div className="text-blue-400 text-sm font-medium">
                  ✓ Completado
                </div>
              </div>

              <div className="flex items-center justify-between p-4 bg-purple-500/10 rounded-lg border border-purple-500/20">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-6 h-6 text-purple-500" />
                  <div>
                    <p className="text-white font-medium">Especialista en Virología</p>
                    <p className="text-purple-200/80 text-sm">Comprensión de estructuras virales</p>
                  </div>
                </div>
                <div className="text-purple-400 text-sm font-medium">
                  ✓ Completado
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Louis Pasteur Quote */}
        <div className="mt-8 bg-slate-800/40 backdrop-blur-xl rounded-3xl shadow-2xl border border-green-700/20 p-8 text-center">
          <div className="flex justify-center mb-4">
            <img
              src="/lovable-uploads/pasteur.png"
              alt="Louis Pasteur"
              className="w-20 h-20 object-contain opacity-80"
            />
          </div>
          <blockquote className="text-lg text-green-200 italic mb-4">
            "La suerte favorece a la mente preparada"
          </blockquote>
          <p className="text-slate-400 text-sm">
            - Louis Pasteur
          </p>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
