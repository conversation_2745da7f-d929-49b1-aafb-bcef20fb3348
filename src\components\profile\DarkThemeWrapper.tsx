import { ReactNode, useEffect } from 'react';

interface DarkThemeWrapperProps {
  children: ReactNode;
}

const DarkThemeWrapper = ({ children }: DarkThemeWrapperProps) => {
  useEffect(() => {
    // Force dark theme
    document.documentElement.classList.add('dark');
    document.body.style.backgroundColor = '#0f172a';
    document.body.style.color = 'white';
    
    // Override any light theme variables
    const style = document.createElement('style');
    style.textContent = `
      .dark {
        --background: 222.2 84% 4.9%;
        --foreground: 210 40% 98%;
        --card: 222.2 84% 4.9%;
        --card-foreground: 210 40% 98%;
        --popover: 222.2 84% 4.9%;
        --popover-foreground: 210 40% 98%;
        --primary: 210 40% 98%;
        --primary-foreground: 222.2 47.4% 11.2%;
        --secondary: 217.2 32.6% 17.5%;
        --secondary-foreground: 210 40% 98%;
        --muted: 217.2 32.6% 17.5%;
        --muted-foreground: 215 20.2% 65.1%;
        --accent: 217.2 32.6% 17.5%;
        --accent-foreground: 210 40% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 210 40% 98%;
        --border: 217.2 32.6% 17.5%;
        --input: 217.2 32.6% 17.5%;
        --ring: 212.7 26.8% 83.9%;
      }
      
      * {
        border-color: hsl(var(--border));
      }
      
      body {
        background-color: #0f172a !important;
        color: white !important;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
      document.body.style.backgroundColor = '';
      document.body.style.color = '';
    };
  }, []);

  return (
    <div 
      className="dark"
      style={{
        backgroundColor: '#0f172a',
        color: 'white',
        minHeight: '100vh'
      }}
    >
      {children}
    </div>
  );
};

export default DarkThemeWrapper;
