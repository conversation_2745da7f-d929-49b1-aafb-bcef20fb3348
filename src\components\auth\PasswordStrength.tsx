
import { useMemo } from "react";
import { CheckCircle, XCircle } from "lucide-react";

interface PasswordStrengthProps {
  password: string;
}

const PasswordStrength = ({ password }: PasswordStrengthProps) => {
  const analysis = useMemo(() => {
    const checks = [
      { label: "Al menos 8 caracteres", test: password.length >= 8 },
      { label: "Una letra mayúscula", test: /[A-Z]/.test(password) },
      { label: "Una letra minúscula", test: /[a-z]/.test(password) },
      { label: "Un número", test: /\d/.test(password) },
      { label: "Un carácter especial", test: /[!@#$%^&*(),.?":{}|<>]/.test(password) },
    ];

    const passedChecks = checks.filter(check => check.test).length;
    let strength = "No te esfuerzas mucho, ¿No?.";
    let strengthColor = "text-red-500";
    let progressColor = "bg-red-500";
    let progressWidth = "20%";


    if (passedChecks >= 5) {
      strength = "Más dura que trámite del Estado";
      strengthColor = "text-green-500";
      progressColor = "bg-green-500";
      progressWidth = "100%";
    } else if (passedChecks >= 4) {
      strength = "Más firme que promesa de político";
      strengthColor = "text-green-400";
      progressColor = "bg-green-400";
      progressWidth = "80%";
    } else if (passedChecks >= 3) {
      strength = "Ni fu ni fa, como día nublado";
      strengthColor = "text-yellow-500";
      progressColor = "bg-yellow-500";
      progressWidth = "60%";
    } else if (passedChecks >= 2) {
      strength = "Te la sacan en dos toques";
      strengthColor = "text-orange-500";
      progressColor = "bg-orange-500";
      progressWidth = "40%";
    }
     
    if (passedChecks >= 5 && password.length >= 12) {
      strength = "Largo como puteada de ta ta tartamudo";
    }

    
    

    return {
      checks,
      strength,
      strengthColor,
      progressColor,
      progressWidth,
      score: passedChecks
    };
  }, [password]);

  return (
    <div className="space-y-3 p-4 bg-slate-800/30 rounded-lg border border-slate-700/50">
      <div className="flex items-center justify-between">
        <span className="text-sm text-slate-300">Seguridad de la contraseña:</span>
        <span className={`text-sm font-medium ${analysis.strengthColor}`}>
          {analysis.strength}
        </span>
      </div>
      
      <div className="w-full bg-slate-700 rounded-full h-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${analysis.progressColor}`}
          style={{ width: analysis.progressWidth }}
        ></div>
      </div>

      <div className="space-y-2">
        {analysis.checks.map((check, index) => (
          <div key={index} className="flex items-center space-x-2">
            {check.test ? (
              <CheckCircle className="w-4 h-4 text-green-500" />
            ) : (
              <XCircle className="w-4 h-4 text-slate-500" />
            )}
            <span className={`text-xs ${check.test ? 'text-green-400' : 'text-slate-400'}`}>
              {check.label}
            </span>
          </div>
        ))}
      </div>

      {analysis.score >= 4 && (
        <div className="flex items-center space-x-2 pt-2 border-t border-slate-700/50">
          <CheckCircle className="w-4 h-4 text-green-500" />
          <span className="text-xs text-green-400 font-medium">
            ¡Excelente! Tu contraseña es segura.
          </span>
        </div>
      )}
    </div>
  );
};

export default PasswordStrength;
