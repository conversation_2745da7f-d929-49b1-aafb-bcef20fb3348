@tailwind base;
@tailwind components;
@tailwind utilities;

/* Force dark theme for profile pages */
.profile-page {
  background-color: #0f172a !important;
  color: white !important;
}

.profile-card {
  background-color: rgba(30, 41, 59, 0.9) !important;
  border-color: rgba(34, 197, 94, 0.4) !important;
}

/* Sonner toast improvements for stacking */
[data-sonner-toaster] {
  --normal-bg: rgba(15, 23, 42, 0.95) !important;
  --normal-border: rgba(71, 85, 105, 0.3) !important;
  --normal-text: white !important;
  --success-bg: rgba(15, 23, 42, 0.95) !important;
  --success-border: rgba(34, 197, 94, 0.4) !important;
  --error-bg: rgba(15, 23, 42, 0.95) !important;
  --error-border: rgba(239, 68, 68, 0.4) !important;
  --warning-bg: rgba(15, 23, 42, 0.95) !important;
  --warning-border: rgba(245, 158, 11, 0.4) !important;
}

[data-sonner-toast] {
  transform: translateY(0) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

[data-sonner-toast][data-expanded="false"] {
  transform: translateY(-8px) scale(0.98) !important;
  opacity: 0.9 !important;
}

[data-sonner-toast][data-expanded="false"]:nth-child(2) {
  transform: translateY(-16px) scale(0.96) !important;
  opacity: 0.8 !important;
}

[data-sonner-toast][data-expanded="false"]:nth-child(3) {
  transform: translateY(-24px) scale(0.94) !important;
  opacity: 0.7 !important;
}

/* Custom Sonner Toast Styles */
[data-sonner-toaster] {
  --normal-bg: rgba(30, 41, 59, 0.95);
  --normal-border: rgba(34, 197, 94, 0.3);
  --normal-text: rgb(255, 255, 255);
  --success-bg: rgba(30, 41, 59, 0.95);
  --success-border: rgba(34, 197, 94, 0.6);
  --error-bg: rgba(30, 41, 59, 0.95);
  --error-border: rgba(239, 68, 68, 0.6);
  --warning-bg: rgba(30, 41, 59, 0.95);
  --warning-border: rgba(245, 158, 11, 0.6);
}

[data-sonner-toast] {
  background: var(--normal-bg) !important;
  border: 1px solid var(--normal-border) !important;
  color: var(--normal-text) !important;
  backdrop-filter: blur(16px) !important;
  border-radius: 12px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(34, 197, 94, 0.1) !important;
  cursor: pointer !important;
  transition: border-color 0.2s ease, box-shadow 0.2s ease !important;
  position: relative !important;
  min-height: 60px !important;
  padding: 16px !important;
  z-index: 50 !important;
}

[data-sonner-toast]:hover {
  border-color: rgba(34, 197, 94, 0.6) !important;
}

[data-sonner-toast][data-type="success"] {
  background: var(--success-bg) !important;
  border-color: var(--success-border) !important;
  box-shadow: 0 25px 50px -12px rgba(34, 197, 94, 0.25), 0 0 0 1px rgba(34, 197, 94, 0.2) !important;
}

[data-sonner-toast][data-type="success"]:hover {
  border-color: rgba(34, 197, 94, 0.8) !important;
}

[data-sonner-toast][data-type="error"] {
  background: var(--error-bg) !important;
  border-color: var(--error-border) !important;
  box-shadow: 0 25px 50px -12px rgba(239, 68, 68, 0.25), 0 0 0 1px rgba(239, 68, 68, 0.2) !important;
}

[data-sonner-toast][data-type="error"]:hover {
  border-color: rgba(239, 68, 68, 0.8) !important;
}

[data-sonner-toast][data-type="warning"] {
  background: var(--warning-bg) !important;
  border-color: var(--warning-border) !important;
  box-shadow: 0 25px 50px -12px rgba(245, 158, 11, 0.25), 0 0 0 1px rgba(245, 158, 11, 0.2) !important;
}

[data-sonner-toast][data-type="warning"]:hover {
  border-color: rgba(245, 158, 11, 0.8) !important;
}

[data-sonner-toast] [data-title] {
  color: rgb(255, 255, 255) !important;
  font-weight: 700 !important;
  font-size: 15px !important;
  line-height: 1.4 !important;
  letter-spacing: 0.025em !important;
  display: block !important;
  margin-bottom: 4px !important;
}

[data-sonner-toast] [data-description] {
  color: rgba(203, 213, 225, 0.9) !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  line-height: 1.5 !important;
  display: block !important;
  margin-top: 2px !important;
}

[data-sonner-toast][data-type="success"] [data-title] {
  color: rgb(255, 255, 255) !important;
  font-weight: 700 !important;
}

[data-sonner-toast][data-type="success"] [data-description] {
  color: rgba(34, 197, 94, 0.95) !important;
  font-weight: 500 !important;
}

[data-sonner-toast][data-type="error"] [data-title] {
  color: rgb(255, 255, 255) !important;
  font-weight: 700 !important;
}

[data-sonner-toast][data-type="error"] [data-description] {
  color: rgba(248, 113, 113, 0.95) !important;
  font-weight: 500 !important;
}

[data-sonner-toast][data-type="warning"] [data-title] {
  color: rgb(255, 255, 255) !important;
  font-weight: 700 !important;
}

[data-sonner-toast][data-type="warning"] [data-description] {
  color: rgba(251, 191, 36, 0.95) !important;
  font-weight: 500 !important;
}

[data-sonner-toast][data-type="info"] [data-title] {
  color: rgb(255, 255, 255) !important;
  font-weight: 700 !important;
}

[data-sonner-toast][data-type="info"] [data-description] {
  color: rgba(96, 165, 250, 0.95) !important;
  font-weight: 500 !important;
}

/* Success icon color */
[data-sonner-toast][data-type="success"] [data-icon] {
  color: rgb(34, 197, 94) !important;
}

/* Error icon color */
[data-sonner-toast][data-type="error"] [data-icon] {
  color: rgb(239, 68, 68) !important;
}

/* Warning icon color */
[data-sonner-toast][data-type="warning"] [data-icon] {
  color: rgb(245, 158, 11) !important;
}

/* Close button styling - Using native Sonner button */
[data-sonner-toast] [data-close-button] {
  background: rgba(30, 41, 59, 0.8) !important;
  border: 1px solid rgba(71, 85, 105, 0.3) !important;
  color: rgba(203, 213, 225, 0.9) !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
  width: 24px !important;
  height: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  opacity: 0.8 !important;
  backdrop-filter: blur(8px) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

[data-sonner-toast] [data-close-button]:hover {
  background: rgba(71, 85, 105, 0.8) !important;
  color: rgb(255, 255, 255) !important;
  opacity: 1 !important;
  border-color: rgba(71, 85, 105, 0.5) !important;
}

[data-sonner-toast] [data-close-button]:active {
  transform: scale(0.95) !important;
}

/* Success toast close button */
[data-sonner-toast][data-type="success"] [data-close-button]:hover {
  background: rgba(34, 197, 94, 0.2) !important;
  border-color: rgba(34, 197, 94, 0.5) !important;
}

/* Error toast close button */
[data-sonner-toast][data-type="error"] [data-close-button]:hover {
  background: rgba(239, 68, 68, 0.2) !important;
  border-color: rgba(239, 68, 68, 0.5) !important;
}

/* Warning toast close button */
[data-sonner-toast][data-type="warning"] [data-close-button]:hover {
  background: rgba(245, 158, 11, 0.2) !important;
  border-color: rgba(245, 158, 11, 0.5) !important;
}

/* Animation improvements */
[data-sonner-toast] {
  animation: sonner-slide-in 0.3s ease-out !important;
}

@keyframes sonner-slide-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Loading bar for duration */
[data-sonner-toast]::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: linear-gradient(90deg, rgba(34, 197, 94, 0.8), rgba(16, 185, 129, 0.8));
  border-radius: 0 0 12px 12px;
  animation: sonner-progress var(--duration, 4s) linear forwards;
}

[data-sonner-toast][data-type="error"]::before {
  background: linear-gradient(90deg, rgba(239, 68, 68, 0.8), rgba(220, 38, 38, 0.8));
}

[data-sonner-toast][data-type="warning"]::before {
  background: linear-gradient(90deg, rgba(245, 158, 11, 0.8), rgba(217, 119, 6, 0.8));
}

@keyframes sonner-progress {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}

/* Pause progress bar on hover */
[data-sonner-toast]:hover::before {
  animation-play-state: paused !important;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}