import { useState } from "react";
import { CheckCircle, XCircle, ArrowRight } from "lucide-react";

interface CountryQuizProps {
  onQuizComplete: (completed: boolean) => void;
}

const CountryQuiz = ({ onQuizComplete }: CountryQuizProps) => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [showResult, setShowResult] = useState(false);
  const [isCorrect, setIsCorrect] = useState(false);
  const [completedQuestions, setCompletedQuestions] = useState<boolean[]>([false, false, false]);

  const questions = [
    {
      id: 0,
      question: "¿De qué país era Louis Pasteur?",
      options: [
        {
          id: "france",
          name: "<PERSON><PERSON><PERSON>",
          image: "/lovable-uploads/france.png",
          isCorrect: true
        },
        {
          id: "germany",
          name: "<PERSON><PERSON><PERSON>",
          image: "/lovable-uploads/germany-map.png",
          isCorrect: false
        },
        {
          id: "england",
          name: "Inglate<PERSON>",
          image: "/lovable-uploads/england-map.png",
          isCorrect: false
        }
      ]
    },
    {
      id: 1,
      question: "¿Cuál fue uno de los principales campos de estudio de Louis Pasteur?",
      options: [
        {
          id: "astronomy",
          name: "Astronomía",
          image: "/lovable-uploads/astronomia.png",
          isCorrect: false
        },
        {
          id: "infectious-diseases",
          name: "Enfermedades Infecciosas",
          image: "/lovable-uploads/enfermedades-infecciosas.png",
          isCorrect: true
        },
        {
          id: "physics",
          name: "Física",
          image: "/lovable-uploads/fisica.png",
          isCorrect: false
        }
      ]
    },
    {
      id: 2,
      question: "En la virología, ¿qué es la 'cápside' de un virus y cuál es su principal función?",
      options: [
        {
          id: "dna-inside",
          name: "El material genético (ADN o ARN) del virus.",
          image: "/lovable-uploads/adn-in-virus.png",
          isCorrect: false
        },
        {
          id: "protein-shield",
          name: "Es una cubierta proteica que protege el material genético",
          image: "/lovable-uploads/proteins-shield-from-virus.png",
          isCorrect: true
        },
        {
          id: "dna-protection",
          name: "Un componente lipídico que rodea la cápside en algunos virus.",
          image: "/lovable-uploads/adn-protect-from-virus.png",
          isCorrect: false  
        }
      ]
    }
  ];

  const currentQuestionData = questions[currentQuestion];

  const handleAnswerSelect = (answerId: string) => {
    if (showResult) return;

    setSelectedAnswer(answerId);
    const selected = currentQuestionData.options.find(option => option.id === answerId);
    const correct = selected?.isCorrect || false;

    setIsCorrect(correct);
    setShowResult(true);

    // Update completed questions
    const newCompletedQuestions = [...completedQuestions];
    newCompletedQuestions[currentQuestion] = correct;
    setCompletedQuestions(newCompletedQuestions);

    // Check if all questions are completed correctly
    const allCompleted = newCompletedQuestions.every(completed => completed);
    onQuizComplete(allCompleted);
  };

  const nextQuestion = () => {
    if (currentQuestion < questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1);
      setSelectedAnswer(null);
      setShowResult(false);
      setIsCorrect(false);
    }
  };

  const resetQuiz = () => {
    setCurrentQuestion(0);
    setSelectedAnswer(null);
    setShowResult(false);
    setIsCorrect(false);
    setCompletedQuestions([false, false, false]);
    onQuizComplete(false);
  };

  return (
    <div className="space-y-4 p-6 bg-white/5 rounded-lg border border-slate-600/30 h-fit backdrop-blur-sm">
      {/* CAPTCHA Header */}
      <div className="flex items-center space-x-3 pb-3 border-b border-slate-600/30">
        <div className="w-6 h-6 bg-blue-500 rounded-sm flex items-center justify-center">
          <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        </div>
        <div>
          <h3 className="text-sm font-medium text-white">
            Verificación de Seguridad
          </h3>
          <p className="text-xs text-slate-400">
            Pregunta {currentQuestion + 1} de {questions.length} • Pasteur CAPTCHA
          </p>
        </div>
      </div>

      {/* Question */}
      <div className="space-y-3">
        <p className="text-sm text-slate-200 font-medium leading-relaxed">
          {currentQuestionData.question}
        </p>
        <p className="text-xs text-slate-400">
          Selecciona la respuesta correcta para continuar
        </p>
      </div>

      {/* CAPTCHA Options Grid */}
      <div className="grid grid-cols-1 gap-3">
        {currentQuestionData.options.map((option) => (
          <label
            key={option.id}
            className={`
              relative flex items-center p-3 rounded border cursor-pointer transition-all duration-200
              ${selectedAnswer === option.id
                ? showResult
                  ? option.isCorrect
                    ? 'border-green-500 bg-green-500/10'
                    : 'border-red-500 bg-red-500/10'
                  : 'border-blue-400 bg-blue-400/10'
                : 'border-slate-500/50 bg-slate-700/20 hover:border-slate-400 hover:bg-slate-700/40'
              }
              ${showResult ? 'cursor-default' : 'cursor-pointer'}
            `}
          >
            <input
              type="radio"
              name="captcha-option"
              value={option.id}
              checked={selectedAnswer === option.id}
              onChange={() => handleAnswerSelect(option.id)}
              disabled={showResult}
              className="sr-only"
            />

            {/* Custom Radio Button */}
            <div className={`
              w-4 h-4 rounded-full border-2 mr-3 flex items-center justify-center transition-colors
              ${selectedAnswer === option.id
                ? showResult
                  ? option.isCorrect
                    ? 'border-green-500 bg-green-500'
                    : 'border-red-500 bg-red-500'
                  : 'border-blue-400 bg-blue-400'
                : 'border-slate-400'
              }
            `}>
              {selectedAnswer === option.id && (
                <div className="w-2 h-2 rounded-full bg-white"></div>
              )}
            </div>

            {/* Option Image */}
            <div className="w-12 h-12 rounded border bg-white/5 flex items-center justify-center mr-3">
              <img
                src={option.image}
                alt={`Opción ${option.name}`}
                className="w-8 h-8 object-contain"
              />
            </div>

            {/* Option Text */}
            <div className="flex-1">
              <span className="text-sm text-slate-200 font-medium">
                {option.name}
              </span>
            </div>

            {/* Result Icon */}
            {showResult && selectedAnswer === option.id && (
              <div className="ml-2">
                {option.isCorrect ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-500" />
                )}
              </div>
            )}
          </label>
        ))}
      </div>

      {/* CAPTCHA Result */}
      {showResult && (
        <div className={`
          p-3 rounded border text-center space-y-2
          ${isCorrect
            ? 'border-green-500/50 bg-green-500/5'
            : 'border-red-500/50 bg-red-500/5'
          }
        `}>
          {isCorrect ? (
            <>
              <div className="flex items-center justify-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-green-400 text-sm font-medium">
                  Verificación exitosa
                </span>
              </div>

              {currentQuestion < questions.length - 1 ? (
                <button
                  onClick={nextQuestion}
                  className="flex items-center justify-center space-x-2 px-3 py-1.5 bg-blue-600 hover:bg-blue-500 text-white rounded text-xs transition-colors mx-auto"
                >
                  <span>Continuar</span>
                  <ArrowRight className="w-3 h-3" />
                </button>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-green-400 text-xs font-medium">
                    ✓ Verificación completa
                  </span>
                </div>
              )}
            </>
          ) : (
            <>
              <div className="flex items-center justify-center space-x-2">
                <XCircle className="w-4 h-4 text-red-500" />
                <span className="text-red-400 text-sm font-medium">
                  Verificación fallida
                </span>
              </div>
              <p className="text-red-300 text-xs">
                A ver estudiado 📚
              </p>
              <button
                onClick={resetQuiz}
                className="px-3 py-1.5 bg-slate-600 hover:bg-slate-500 text-white rounded text-xs transition-colors"
              >
                Reintentar
              </button>
            </>
          )}
        </div>
      )}

      {/* CAPTCHA Progress */}
      <div className="flex items-center justify-between pt-3 border-t border-slate-600/30">
        <div className="flex space-x-1">
          {questions.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-colors ${
                index === currentQuestion
                  ? 'bg-blue-400'
                  : completedQuestions[index]
                  ? 'bg-green-500'
                  : 'bg-slate-500'
              }`}
            />
          ))}
        </div>

        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 border border-slate-400 rounded-sm flex items-center justify-center">
            {completedQuestions.every(completed => completed) && (
              <CheckCircle className="w-3 h-3 text-green-500" />
            )}
          </div>
          <span className="text-xs text-slate-400">
            No soy un robot
          </span>
        </div>
      </div>
    </div>
  );
};

export default CountryQuiz;
